# DocuSign System with Google Sheets & Apps Script Backend

This document provides step-by-step instructions to configure and deploy the DocuSign System application. The frontend application communicates with a Google Apps Script backend, which uses Google Drive for file storage and a Google Sheet as a database.

## Prerequisites

- A Google Account.
- Basic familiarity with Google Drive and Google Sheets.

---

## **Part 1: Set Up Google Drive & Google Sheet**

### 1. Create Google Drive Folders

You need two folders in your Google Drive to store the documents.

1.  Go to [Google Drive](https://drive.google.com).
2.  Create a new folder named `DocuSign_Uploads`.
3.  Create another new folder named `DocuSign_Signed`.
4.  **Get the Folder IDs:**
    *   Right-click on the `DocuSign_Uploads` folder and select "Share" -> "Copy link".
    *   The link will look like `https://drive.google.com/drive/folders/SOME_LONG_ID?usp=sharing`.
    *   Copy the `SOME_LONG_ID` part. This is your **Uploads Folder ID**. Save it in a text editor for now.
    *   Repeat the process for the `DocuSign_Signed` folder to get your **Signed Folder ID**.

### 2. <PERSON><PERSON> and Prepare the Google Sheet

This sheet will act as your database.

1.  Go to [Google Sheets](https://sheets.google.com).
2.  Create a new, blank spreadsheet. Name it `DocuSign_Monitoring`.
3.  **Get the Sheet ID:**
    *   The URL of your new sheet will be `https://docs.google.com/spreadsheets/d/SOME_LONG_ID/edit`.
    *   Copy the `SOME_LONG_ID` part. This is your **Sheet ID**. Save it for later.
4.  **Set up the headers:** In the first row of your sheet, enter the following headers, one in each cell from A1 to L1:
    - `id`
    - `name`
    - `status`
    - `uploaderId`
    - `uploaderName`
    - `subject`
    - `details`
    - `isUrgent`
    - `uploadedAt`
    - `signedAt`
    - `uploadFileUrl`
    - `signedFileUrl`

---

## **Part 2: Deploy the Google Apps Script Backend**

### 1. Create the Script

1.  Go to [Google Apps Script](https://script.google.com).
2.  Click **New project**.
3.  Give the project a name, for example, "DocuSign Backend".
4.  Delete the placeholder code in the `Code.gs` file.
5.  Copy the **entire content** from the `gscript.js.txt` file provided with the application source code.
6.  Paste it into the Apps Script editor.

### 2. Configure the Script

At the top of the script, you'll see a `CONFIG` section. Replace the placeholder values with the IDs you saved earlier.

```javascript
const CONFIG = {
  SHEET_ID: 'PASTE_YOUR_SHEET_ID_HERE',
  UPLOADS_FOLDER_ID: 'PASTE_YOUR_UPLOADS_FOLDER_ID_HERE',
  SIGNED_FOLDER_ID: 'PASTE_YOUR_SIGNED_FOLDER_ID_HERE',
};
```

### 3. Deploy the Script as a Web App

1.  Click the **Deploy** button in the top right, then select **New deployment**.
2.  Click the gear icon next to "Select type" and choose **Web app**.
3.  In the "New deployment" dialog:
    *   **Description:** `DocuSign Backend v1` (or any description you like).
    *   **Execute as:** `Me (<EMAIL>)`.
    *   **Who has access:** `Anyone` (This is important! It allows the frontend to call the script without requiring users to log into Google).
4.  Click **Deploy**.
5.  **Authorize permissions:** Google will ask you to authorize the script's access to your Drive and Sheets.
    *   Click **Authorize access**.
    *   Choose your Google account.
    *   You may see a "Google hasn't verified this app" warning. This is normal for your own scripts. Click **Advanced**, then click **Go to [Your Script Name] (unsafe)**.
    *   Review the permissions and click **Allow**.
6.  After deployment, you will see a dialog with a **Web app URL**. Click the **Copy** button. This is your deployment URL.

---

## **Part 3: Configure the Frontend Application**

1.  Open the `config.ts` file in your frontend application code.
2.  You will see the following line:
    ```typescript
    export const SCRIPT_URL = 'PASTE_YOUR_WEB_APP_URL_HERE';
    ```
3.  Replace `PASTE_YOUR_WEB_APP_URL_HERE` with the Web app URL you just copied.
4.  Save the file.

---

## **You're Done!**

Your application is now fully configured. You can run the frontend application, and it will communicate with your new backend to manage all your documents.
