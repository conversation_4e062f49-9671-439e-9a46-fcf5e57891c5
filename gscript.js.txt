/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

// --- CONFIGURATION ---
// 1. Create two folders in Google Drive for uploads and signed documents.
// 2. Create a Google Sheet to act as the database.
// 3. Paste the IDs of the folders and the sheet below.
const CONFIG = {
  SHEET_ID: 'PASTE_YOUR_SHEET_ID_HERE',
  UPLOADS_FOLDER_ID: 'PASTE_YOUR_UPLOADS_FOLDER_ID_HERE',
  SIGNED_FOLDER_ID: 'PASTE_YOUR_SIGNED_FOLDER_ID_HERE',
};

const sheet = SpreadsheetApp.openById(CONFIG.SHEET_ID).getSheets()[0];
const uploadsFolder = DriveApp.getFolderById(CONFIG.UPLOADS_FOLDER_ID);
const signedFolder = DriveApp.getFolderById(CONFIG.SIGNED_FOLDER_ID);


// --- ROUTING ---

// Handles GET requests to fetch all document data
function doGet(e) {
  try {
    const data = getDocuments();
    return ContentService.createTextOutput(JSON.stringify({ status: 'success', data: data }))
      .setMimeType(ContentService.MimeType.JSON);
  } catch (error) {
    return ContentService.createTextOutput(JSON.stringify({ status: 'error', message: error.message }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// Handles POST requests for actions like uploading and signing
function doPost(e) {
  try {
    const request = JSON.parse(e.postData.contents);
    let response;

    switch (request.action) {
      case 'upload':
        response = handleUpload(request.payload);
        break;
      case 'sign':
        response = handleSign(request.payload);
        break;
      default:
        throw new Error('Invalid action specified');
    }

    return ContentService.createTextOutput(JSON.stringify({ status: 'success', data: response }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    Logger.log(error);
    return ContentService.createTextOutput(JSON.stringify({ status: 'error', message: error.message, stack: error.stack }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}


// --- DATA FETCHING ---

function getDocuments() {
  const range = sheet.getDataRange();
  const values = range.getValues();
  const headers = values.shift(); // Remove header row

  const documents = values.map((row, index) => {
    const doc = {};
    headers.forEach((header, i) => {
      doc[header] = row[i];
    });
    // Use row number + 1 as a stable ID (since sheet rows are 1-indexed and we shifted headers)
    doc.id = index + 2; 
    return doc;
  });

  return documents.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt));
}


// --- ACTIONS ---

function handleUpload(payload) {
  const { fileData, fileName, fileType, uploaderId, uploaderName, subject, details, isUrgent } = payload;
  
  // Decode base64 and create the file
  const decoded = Utilities.base64Decode(fileData, Utilities.Charset.UTF_8);
  const blob = Utilities.newBlob(decoded, fileType, fileName);
  const newFile = uploadsFolder.createFile(blob);
  newFile.setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW); // Make it viewable

  const newRow = [
    '', // id is managed by row number
    fileName,
    'Pending Signature',
    uploaderId,
    uploaderName,
    subject || '',
    details || '',
    isUrgent || false,
    new Date().toISOString(),
    '', // signedAt
    newFile.getUrl(), // uploadFileUrl
    ''  // signedFileUrl
  ];

  sheet.appendRow(newRow);
  
  // Set the formula for the ID in the first column
  const newRowIndex = sheet.getLastRow();
  sheet.getRange(newRowIndex, 1).setValue(newRowIndex);

  return { message: 'Upload successful', fileName: fileName };
}


function handleSign(payload) {
  const { fileData, fileName, fileType, documentId } = payload;

  // Decode base64 and create the signed file
  const decoded = Utilities.base64Decode(fileData, Utilities.Charset.UTF_8);
  const blob = Utilities.newBlob(decoded, fileType, fileName);
  const signedFile = signedFolder.createFile(blob);
  signedFile.setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW);

  const range = sheet.getRange(documentId, 1, 1, sheet.getLastColumn());
  const values = range.getValues()[0];
  
  // Find column indices
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  const statusIndex = headers.indexOf('status') + 1;
  const signedAtIndex = headers.indexOf('signedAt') + 1;
  const signedFileUrlIndex = headers.indexOf('signedFileUrl') + 1;
  const nameIndex = headers.indexOf('name') + 1;


  if (!statusIndex || !signedAtIndex || !signedFileUrlIndex || !nameIndex) {
    throw new Error('Could not find required columns in the sheet.');
  }
  
  // Update the row with new data
  sheet.getRange(documentId, statusIndex).setValue('Signed');
  sheet.getRange(documentId, signedAtIndex).setValue(new Date().toISOString());
  sheet.getRange(documentId, signedFileUrlIndex).setValue(signedFile.getUrl());
  sheet.getRange(documentId, nameIndex).setValue(fileName); // Update the name to the signed file's name


  return { message: 'Document signed successfully', documentId: documentId };
}
