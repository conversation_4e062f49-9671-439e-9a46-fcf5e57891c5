:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --tertiary-color: #6b7280;
  --tertiary-hover: #4b5563;
  --text-color: #111827;
  --text-light: #6b7280;
  --text-white: #ffffff;
  --bg-color: #f9fafb;
  --surface-color: #ffffff;
  --border-color: #d1d5db;
  --error-color: #ef4444;
  --status-pending-bg: #fef3c7;
  --status-pending-text: #92400e;
  --status-signed-bg: #d1fae5;
  --status-signed-text: #065f46;
  --font-family: 'Inter', sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

#root {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
}

/* --- Global App Error --- */
.app-error-container {
    position: fixed;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--error-color);
    color: var(--text-white);
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    z-index: 2000;
    font-weight: 500;
}

/* --- Loading Spinner --- */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}


/* --- Login Page --- */
.login-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 5vh;
}

.login-box {
    width: 100%;
    max-width: 400px;
    background-color: var(--surface-color);
    padding: 2.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    text-align: center;
}

.login-box .title {
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-light);
    margin-bottom: 2rem;
}

.login-box form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: left;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-group input,
.form-group textarea {
    font-family: var(--font-family);
    font-size: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(79 70 229 / 0.2);
}

.form-group-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.form-group-checkbox label {
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
}

.login-error {
    color: var(--error-color);
    font-size: 0.875rem;
    text-align: center;
    margin-top: -1rem;
}

.login-button {
    margin-top: 0.5rem;
}


.app-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-color);
}

.header-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 500;
  color: var(--text-light);
}

.logout-button {
    background-color: var(--tertiary-color);
}

.logout-button:hover {
    background-color: var(--tertiary-hover);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* --- Dashboard Navigation --- */
.dashboard-nav {
  display: flex;
  gap: 0.5rem;
  border-bottom: 2px solid var(--border-color);
}

.tab-button {
  font-family: var(--font-family);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-light);
  background-color: transparent;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px; /* Aligns with the container's border */
  transition: color 0.2s, border-color 0.2s;
}

.tab-button:hover {
  color: var(--text-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}


.uploader-section {
    background-color: var(--surface-color);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.uploader-section h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 0.5rem;
    padding: 2rem;
    transition: border-color 0.2s, background-color 0.2s;
}

.drop-zone.drag-over {
    border-color: var(--primary-color);
    background-color: #f0fdf4;
}

.drop-zone p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.button {
    font-family: var(--font-family);
    font-weight: 600;
    padding: 0.65rem 1.25rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s, opacity 0.2s;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.button-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.button-secondary {
    background-color: var(--secondary-color);
    color: var(--text-white);
}

.button-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
}

.button-tertiary {
    background-color: var(--tertiary-color);
    color: var(--text-white);
}
.button-tertiary:hover:not(:disabled) {
    background-color: var(--tertiary-hover);
}

/* --- Admin's Signed Uploader --- */
.admin-uploader {
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    box-shadow: none;
    text-align: left;
}

.admin-uploader h2 {
    text-align: left;
}

.admin-upload-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.admin-upload-form .form-group {
    gap: 0.5rem;
}

.admin-upload-form input,
.admin-upload-form select {
    font-family: var(--font-family);
    font-size: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--surface-color);
    width: 100%;
}

.admin-upload-form input[type="file"] {
    padding: 0.5rem;
}

.admin-upload-form input:focus,
.admin-upload-form select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(79 70 229 / 0.2);
}

.admin-upload-form button {
    align-self: flex-start;
}

.document-list {
  background-color: var(--surface-color);
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-list.empty {
    text-align: center;
    color: var(--text-light);
    padding: 3rem 2rem;
    border: 2px dashed var(--border-color);
    background-color: var(--surface-color);
    box-shadow: none;
}

.document-list h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  transition: background-color 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.document-item:hover {
    background-color: #f9fafb;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.document-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex-grow: 1;
    overflow: hidden;
    min-width: 0;
}

.document-name-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.document-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.urgent-tag {
    background-color: var(--error-color);
    color: var(--text-white);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.15rem 0.5rem;
    border-radius: 9999px;
    flex-shrink: 0;
}

.document-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.document-date {
    font-size: 0.8rem;
    color: var(--text-light);
}

.document-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-left: 1rem;
    flex-shrink: 0;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-pending-signature {
    background-color: var(--status-pending-bg);
    color: var(--status-pending-text);
}

.status-signed {
    background-color: var(--status-signed-bg);
    color: var(--status-signed-text);
}

/* --- Modal --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: var(--surface-color);
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 2rem;
  line-height: 1;
  cursor: pointer;
  color: var(--text-light);
  padding: 0;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
}

.modal-body .form-group {
    text-align: left;
    margin-bottom: 1rem;
}

#upload-details-form .modal-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.file-list-info {
    font-weight: 500;
    color: var(--text-color);
}

.file-list-preview {
    list-style: none;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    max-height: 100px;
    overflow-y: auto;
    font-size: 0.875rem;
    color: var(--text-light);
}


.preview-content {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  text-align: center;
  display: block;
  margin: 0 auto;
}

.preview-placeholder {
    padding: 2rem;
    background-color: var(--bg-color);
    border-radius: 0.5rem;
    color: var(--text-light);
    text-align: center;
}

.preview-placeholder h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.preview-details {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: left;
}

.preview-details h4 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.preview-details p {
    color: var(--text-light);
    white-space: pre-wrap; /* Respects newlines in the details */
}

.preview-metadata {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-light);
}

.preview-metadata p {
    margin: 0;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
