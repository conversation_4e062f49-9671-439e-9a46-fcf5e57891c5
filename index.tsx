/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import { SCRIPT_URL } from './config.js';

// --- TYPES ---
interface AppDocument {
  id: number;
  name: string;
  status: 'Pending Signature' | 'Signed';
  uploaderId: number;
  uploaderName: string;
  subject?: string;
  details?: string;
  isUrgent?: boolean;
  uploadedAt: string;
  signedAt?: string;
  uploadFileUrl: string;
  signedFileUrl?: string;
}

type View = 'login' | 'user' | 'admin';
type Tab = 'uploads' | 'signed';
type LoggedInUser = { id: number; username: string; role: 'user' | 'admin' } | null;

// --- STATE ---
let state: {
  documents: AppDocument[];
  currentView: View;
  activeTab: Tab;
  loggedInUser: LoggedInUser;
  loginError: string | null;
  previewingDocument: AppDocument | null;
  uploadQueue: File[] | null;
  isLoading: boolean;
  appError: string | null;
} = {
  documents: [],
  currentView: 'login',
  activeTab: 'uploads',
  loggedInUser: null,
  loginError: null,
  previewingDocument: null,
  uploadQueue: null,
  isLoading: false,
  appError: null,
};

// --- PREDEFINED USERS ---
const USERS = {
    user: { id: 1, password: 'password123', role: 'user' as 'user' | 'admin' },
    admin: { id: 99, password: 'admin123', role: 'admin' as 'user' | 'admin' },
};


// --- DOM & RENDERING ---
const root = document.getElementById('root')!;

function h<K extends keyof HTMLElementTagNameMap>(
  tag: K,
  props: Record<string, any> | null,
  ...children: (Node | string | null | false)[]
): HTMLElementTagNameMap[K] {
  const el = document.createElement(tag);
  if (props) {
    for (const [key, value] of Object.entries(props)) {
      if (key.startsWith('on') && typeof value === 'function') {
        el.addEventListener(key.substring(2).toLowerCase(), value);
      } else if (value !== false && value != null) {
        el.setAttribute(key, value);
      }
    }
  }
  children.forEach((child) => {
    if (child) {
        if (typeof child === 'string') {
          el.appendChild(document.createTextNode(child));
        } else {
          el.appendChild(child);
        }
    }
  });
  return el;
}

function render() {
  root.innerHTML = '';

  if (state.currentView === 'login') {
      root.appendChild(LoginPage());
  } else {
      root.appendChild(DashboardView());
  }

  if (state.previewingDocument) {
      root.appendChild(PreviewModal(state.previewingDocument));
  }

  if (state.uploadQueue) {
      root.appendChild(UploadDetailsModal(state.uploadQueue));
  }

  if (state.isLoading) {
      root.appendChild(LoadingSpinner());
  }

  if (state.appError) {
      root.appendChild(AppError(state.appError));
  }
}

// --- API & HELPERS ---

async function apiCall(action: string, payload: any) {
    state.isLoading = true;
    state.appError = null;
    render();

    try {
        const response = await fetch(SCRIPT_URL, {
            method: 'POST',
            mode: 'cors',
            credentials: 'omit',
            headers: {
                'Content-Type': 'text/plain;charset=utf-8', // Required for Apps Script
            },
            body: JSON.stringify({ action, payload }),
            redirect: 'follow',
        });
        
        const result = await response.json();

        if (result.status === 'error') {
            throw new Error(result.message || 'Unknown backend error');
        }
        return result.data;
    } catch (error: any) {
        state.appError = `API Error: ${error.message}`;
        console.error('API Call Failed:', error);
        throw error; // Re-throw to be caught by the caller
    } finally {
        state.isLoading = false;
        // Don't render here, let the caller decide
    }
}

async function fetchDocuments() {
    if (SCRIPT_URL.includes('PASTE_YOUR_WEB_APP_URL_HERE')) {
        state.appError = "Configuration missing. Please update config.ts with your Web App URL.";
        render();
        return;
    }

    state.isLoading = true;
    state.appError = null;
    render();

    try {
        // Apps Script Web Apps can redirect. We need to handle this.
        let response = await fetch(SCRIPT_URL);
        
        // This handles the redirect from the initial POST-like GET
        if (response.type === 'opaque' || response.redirected) {
             // To bypass CORS issues on redirect, we cannot use fetch.
             // Instead, we just assume the API structure and use a GET on the final URL.
             // This is a common pattern for simple Apps Script GET requests.
             response = await fetch(SCRIPT_URL, {method: 'GET', mode: 'cors'});
        }

        const result = await response.json();
        
        if (result.status === 'error') {
            throw new Error(result.message);
        }

        state.documents = result.data;
    } catch (error: any) {
        state.appError = `Failed to fetch documents: ${error.message}`;
    } finally {
        state.isLoading = false;
        render();
    }
}

function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const result = (reader.result as string).split(',')[1];
            resolve(result);
        };
        reader.onerror = error => reject(error);
    });
}

function formatDateTime(dateString: string): string {
  if (!dateString) return '';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  }).format(new Date(dateString));
}

// --- COMPONENTS ---

function LoadingSpinner() {
    return h('div', { class: 'loading-overlay' }, h('div', { class: 'spinner' }));
}

function AppError(message: string) {
    return h('div', { class: 'app-error-container', onclick: () => { state.appError = null; render(); } },
        h('p', {}, message)
    );
}

function LoginPage() {
    return h('div', { class: 'login-container' },
        h('div', { class: 'login-box' },
            h('h1', { class: 'title' }, 'DocuSign System'),
            h('p', { class: 'login-subtitle' }, 'Powered by Google Sheets & Drive'),
            h('form', { onsubmit: handleLogin },
                h('div', { class: 'form-group' },
                    h('label', { for: 'username' }, 'Username'),
                    h('input', { type: 'text', id: 'username', name: 'username', required: true, placeholder: 'user or admin' })
                ),
                h('div', { class: 'form-group' },
                    h('label', { for: 'password' }, 'Password'),
                    h('input', { type: 'password', id: 'password', name: 'password', required: true, placeholder: 'password123 or admin123' })
                ),
                state.loginError && h('p', { class: 'login-error' }, state.loginError),
                h('button', { type: 'submit', class: 'button button-primary login-button' }, 'Login')
            )
        )
    );
}

function DashboardView() {
    const isUserView = state.currentView === 'user';
    const isAdminView = state.currentView === 'admin';

    const documentsForCurrentTab = state.documents.filter(doc => {
        const isSigned = doc.status === 'Signed';
        const belongsToUser = state.loggedInUser && doc.uploaderId === state.loggedInUser.id;

        if (isAdminView) {
            return state.activeTab === 'signed' ? isSigned : !isSigned;
        }
        if (!belongsToUser) return false;
        return state.activeTab === 'signed' ? isSigned : !isSigned;
    });

    return h('div', { class: 'app-container' },
        Header(),
        h('main', { class: 'main-content' },
            h('nav', { class: 'dashboard-nav' },
                h('button', {
                    class: `tab-button ${state.activeTab === 'uploads' ? 'active' : ''}`,
                    onclick: () => handleTabChange('uploads')
                }, isUserView ? 'My Uploads' : 'Pending Documents'),
                h('button', {
                    class: `tab-button ${state.activeTab === 'signed' ? 'active' : ''}`,
                    onclick: () => handleTabChange('signed')
                }, 'Signed Documents')
            ),
            h('div', { class: 'tab-content' },
                isUserView && state.activeTab === 'uploads' && FileUploader(),
                isAdminView && state.activeTab === 'signed' && AdminSignedUploader(),
                DocumentList({ documents: documentsForCurrentTab })
            )
        )
    );
}

function Header() {
  return h('header', { class: 'header' },
    h('h1', { class: 'title' }, 'DocuSign System'),
    state.loggedInUser && h('div', { class: 'header-user-info' },
      h('span', {}, `Welcome, ${state.loggedInUser.username}`),
      h('button', { class: 'button button-secondary logout-button', onclick: handleLogout }, 'Logout')
    )
  );
}

function FileUploader() {
  return h('section', { class: 'uploader-section' },
    h('h2', {}, 'Upload a document for signature'),
    h('div', {
        class: 'drop-zone',
        ondragover: (e: DragEvent) => { e.preventDefault(); (e.currentTarget as HTMLDivElement).classList.add('drag-over'); },
        ondragleave: (e: DragEvent) => { e.preventDefault(); (e.currentTarget as HTMLDivElement).classList.remove('drag-over'); },
        ondrop: handleFileDrop
    },
      h('p', {}, 'Drag & drop files here, or'),
      h('label', { for: 'file-input', class: 'button button-primary' }, 'Browse Files'),
      h('input', { type: 'file', id: 'file-input', onchange: handleFileUpload, style: 'display: none;', multiple: true })
    )
  );
}

function AdminSignedUploader() {
    const userList = Object.entries(USERS).filter(([name]) => name !== 'admin');
    return h('section', { class: 'uploader-section admin-uploader' }, h('p', {}, 'Admin direct upload is not supported in this version. Please use the signing workflow.'));
}

function DocumentList({ documents }: { documents: AppDocument[] }) {
    const isUserView = state.currentView === 'user';
    let title = state.activeTab === 'uploads' ? (isUserView ? 'My Uploads' : 'Pending Documents') : 'Signed Documents';
    let emptyMessage = `No ${title.toLowerCase()} found.`;
    if (!documents.length) {
        return h('div', { class: 'document-list empty' }, h('p', {}, emptyMessage));
    }
    return h('section', { class: 'document-list' }, h('h2', {}, title), ...documents.map(doc => DocumentItem(doc)));
}

function DocumentItem(doc: AppDocument) {
  const dateToShow = doc.signedAt || doc.uploadedAt;
  const dateLabel = doc.signedAt ? 'Signed' : 'Uploaded';

  return h('div', { class: 'document-item', onclick: () => handlePreview(doc) },
    h('div', { class: 'document-info' },
      h('div', { class: 'document-name-wrapper' },
          h('span', { class: 'document-name', title: doc.subject }, doc.name),
          doc.isUrgent && h('span', { class: 'urgent-tag' }, 'Urgent')
      ),
      h('div', { class: 'document-meta' },
        h('span', { class: `status-badge status-${doc.status.toLowerCase().replace(/ /g, '-')}` }, doc.status),
        h('span', { class: 'document-date' }, `• ${dateLabel} ${formatDateTime(dateToShow)}`)
      )
    ),
    h('div', { class: 'document-actions' },
        h('button', {
          class: 'button button-tertiary',
          onclick: (e: MouseEvent) => handleDownload(e, doc)
      }, 'Download')
    )
  );
}

function UploadDetailsModal(files: File[]) {
    return h('div', { class: 'modal-overlay', onclick: handleCancelUpload },
        h('div', { class: 'modal', onclick: e => e.stopPropagation() },
            h('form', { id: 'upload-details-form', onsubmit: handleConfirmUpload },
                h('div', { class: 'modal-header' }, h('h3', {}, 'Upload Details')),
                h('div', { class: 'modal-body' },
                    h('p', { class: 'file-list-info' }, `Uploading ${files.length} file(s):`),
                    h('ul', { class: 'file-list-preview' }, ...files.map(file => h('li', {}, file.name))),
                    h('div', { class: 'form-group' },
                        h('label', { for: 'subject' }, 'Subject'),
                        h('input', { type: 'text', id: 'subject', name: 'subject', required: true, placeholder: 'e.g., Q3 Report for Signature' })
                    ),
                    h('div', { class: 'form-group' },
                        h('label', { for: 'details' }, 'Brief Details (Optional)'),
                        h('textarea', { id: 'details', name: 'details', rows: '3', placeholder: 'e.g., Please sign page 4.' })
                    ),
                    h('div', { class: 'form-group-checkbox' },
                        h('input', { type: 'checkbox', id: 'isUrgent', name: 'isUrgent' }),
                        h('label', { for: 'isUrgent' }, 'Mark as Urgent')
                    )
                ),
                h('div', { class: 'modal-footer' },
                    h('button', { type: 'button', class: 'button button-tertiary', onclick: handleCancelUpload }, 'Cancel'),
                    h('button', { type: 'submit', class: 'button button-primary' }, 'Upload')
                )
            )
        )
    );
}

function PreviewModal(doc: AppDocument) {
    const isPending = doc.status === 'Pending Signature';

    return h('div', { class: 'modal-overlay', onclick: handleClosePreview },
        h('div', { class: 'modal', onclick: e => e.stopPropagation() },
            h('div', { class: 'modal-header' },
                h('h3', {}, doc.name),
                h('button', { class: 'close-button', onclick: handleClosePreview }, '×')
            ),
            h('div', { class: 'modal-body' },
                h('div', { class: 'preview-placeholder' }, h('h4', {}, 'Document Details')),
                (doc.subject || doc.details) && h('div', { class: 'preview-details' },
                    doc.subject && h('h4', {}, `Subject: ${doc.subject}`),
                    doc.details && h('p', {}, doc.details)
                ),
                h('div', { class: 'preview-metadata' },
                    h('p', {}, `Uploaded By: ${doc.uploaderName}`),
                    h('p', {}, `Uploaded: ${formatDateTime(doc.uploadedAt)}`),
                    doc.signedAt && h('p', {}, `Signed: ${formatDateTime(doc.signedAt)}`)
                )
            ),
            h('div', { class: 'modal-footer' },
                h('button', { class: 'button button-tertiary', onclick: (e) => handleDownload(e, doc) }, 'Download'),
                state.currentView === 'admin' && isPending && h('button', {
                    class: 'button button-primary',
                    onclick: (e) => handleSignDocument(e, doc.id)
                }, 'Sign & Upload')
            )
        )
    );
}

// --- EVENT HANDLERS & LOGIC ---

function handleTabChange(tab: Tab) {
    state.activeTab = tab;
    render();
}

async function handleLogin(event: Event) {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const username = (form.elements.namedItem('username') as HTMLInputElement).value;
    const password = (form.elements.namedItem('password') as HTMLInputElement).value;

    const userEntry = Object.entries(USERS).find(([name]) => name === username);

    if (userEntry) {
        const [name, credentials] = userEntry;
        if (credentials.password === password) {
            state.loggedInUser = { id: credentials.id, username: name, role: credentials.role };
            state.currentView = credentials.role;
            state.activeTab = 'uploads';
            state.loginError = null;
            await fetchDocuments(); // Initial data fetch
            return;
        }
    }

    state.loginError = 'Invalid username or password.';
    render();
}

function handleLogout() {
    state.loggedInUser = null;
    state.currentView = 'login';
    state.documents = [];
    render();
}

function handleFileDrop(event: DragEvent) {
    event.preventDefault();
    (event.currentTarget as HTMLDivElement).classList.remove('drag-over');
    if (event.dataTransfer?.files?.length) {
        state.uploadQueue = Array.from(event.dataTransfer.files);
        render();
    }
}

function handleFileUpload(event: Event) {
  const input = event.target as HTMLInputElement;
  if (input.files?.length) {
    state.uploadQueue = Array.from(input.files);
    render();
    input.value = '';
  }
}

async function handleConfirmUpload(event: Event) {
    event.preventDefault();
    if (!state.loggedInUser || !state.uploadQueue) return;

    const form = event.currentTarget as HTMLFormElement;
    const subject = (form.elements.namedItem('subject') as HTMLInputElement).value;
    const details = (form.elements.namedItem('details') as HTMLTextAreaElement).value;
    const isUrgent = (form.elements.namedItem('isUrgent') as HTMLInputElement).checked;

    const filesToUpload = state.uploadQueue;
    state.uploadQueue = null; // Close modal immediately

    const uploadPromises = filesToUpload.map(async file => {
        const fileData = await fileToBase64(file);
        const payload = {
            fileData,
            fileName: file.name,
            fileType: file.type,
            uploaderId: state.loggedInUser!.id,
            uploaderName: state.loggedInUser!.username,
            subject,
            details,
            isUrgent,
        };
        return apiCall('upload', payload);
    });

    try {
        await Promise.all(uploadPromises);
    } catch (error) {
        // Error is already handled by apiCall
    } finally {
        await fetchDocuments();
    }
}

function handleCancelUpload() {
    state.uploadQueue = null;
    render();
}

function handleSignDocument(event: MouseEvent, docId: number) {
  event.stopPropagation();
  const fileInput = h('input', { type: 'file', accept: '*/*', style: 'display: none;' });

  fileInput.onchange = async () => {
      const signedFile = fileInput.files?.[0];
      if (signedFile) {
          try {
              const fileData = await fileToBase64(signedFile);
              await apiCall('sign', {
                  documentId: docId,
                  fileData,
                  fileName: signedFile.name,
                  fileType: signedFile.type,
              });
              handleClosePreview();
              state.activeTab = 'signed';
          } catch (error) {
             // Error already handled
          } finally {
              await fetchDocuments();
          }
      }
      document.body.removeChild(fileInput);
  };

  document.body.appendChild(fileInput);
  fileInput.click();
}

function handlePreview(doc: AppDocument) {
    state.previewingDocument = doc;
    render();
}

function handleClosePreview() {
    state.previewingDocument = null;
    render();
}

function handleDownload(event: MouseEvent, doc: AppDocument) {
    event.stopPropagation();
    const url = doc.status === 'Signed' ? doc.signedFileUrl : doc.uploadFileUrl;
    if (url) {
        window.open(url, '_blank');
    } else {
        alert('No file URL available for this document.');
    }
}

// --- INITIAL RENDER ---
render();
